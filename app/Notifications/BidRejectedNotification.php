<?php

namespace App\Notifications;

use App\Models\Bid;
use App\Models\EmailTemplate;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class BidRejectedNotification extends Notification
{
    use Queueable;

    private $bid;

    /**
     * Create a new notification instance.
     */
    public function __construct(Bid $bid)
    {
        $this->bid = $bid;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $content = EmailTemplate::where('slug', 'bid-rejected-provider')->first();
        
        if (!$content) {
            return (new MailMessage)
                ->subject("Update on Your Bid Submission")
                ->greeting('Hello ' . $notifiable->name . ',')
                ->line("Thank you for submitting a bid for the job '{$this->bid->jobBooking->service_category}'.")
                ->line("Unfortunately, another bid has been selected for this project.")
                ->line("Job Details:")
                ->line("Project Code: {$this->bid->jobBooking->project_code}")
                ->line("Your Bid Amount: $" . number_format($this->bid->amount, 2))
                ->line("Don't be discouraged! There are many more opportunities available.")
                ->action('Browse Available Jobs', url('/job-bookings'))
                ->line('Thank you for being part of our platform. We appreciate your participation!')
                ->line('Keep bidding and good luck with future projects!');
        }

        $locale = request()->hasHeader('Accept-Language') ? 
                  request()->header('Accept-Language') : 
                  app()->getLocale();

        $data = [
            '{{provider_name}}' => $notifiable->name,
            '{{job_title}}' => $this->bid->jobBooking->service_category,
            '{{project_code}}' => $this->bid->jobBooking->project_code,
            '{{bid_amount}}' => '$' . number_format($this->bid->amount, 2),
            '{{job_description}}' => $this->bid->jobBooking->description,
            '{{company_name}}' => config('app.name'),
        ];

        $emailContent = str_replace(array_keys($data), array_values($data), $content->content[$locale]);
        
        return (new MailMessage) 
            ->subject($content->title[$locale])
            ->markdown('emails.email-template', [
                'content' => $content,
                'emailContent' => $emailContent,
                'locale' => $locale
            ]);
    }
    
    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'bid_id' => $this->bid->id,
            'job_booking_id' => $this->bid->jobBooking->id,
            'job_uuid' => $this->bid->jobBooking->job_uuid,
            'project_code' => $this->bid->jobBooking->project_code,
            'bid_amount' => $this->bid->amount,
            'message' => "Your bid for the job '{$this->bid->jobBooking->service_category}' was not selected. Thank you for your participation!",
        ];
    }
}
