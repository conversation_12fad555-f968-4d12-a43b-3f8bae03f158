<?php

namespace App\Notifications;

use App\Models\Bid;
use App\Models\EmailTemplate;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class BidAcceptedNotification extends Notification
{
    use Queueable;

    private $bid;

    /**
     * Create a new notification instance.
     */
    public function __construct(Bid $bid)
    {
        $this->bid = $bid;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $content = EmailTemplate::where('slug', 'bid-accepted-provider')->first();
        
        if (!$content) {
            return (new MailMessage)
                ->subject("Congratulations! Your Bid Has Been Accepted")
                ->greeting('Hello ' . $notifiable->name . ',')
                ->line("Great news! Your bid for the job '{$this->bid->jobBooking->service_category}' has been accepted.")
                ->line("Job Details:")
                ->line("Project Code: {$this->bid->jobBooking->project_code}")
                ->line("Accepted Bid Amount: $" . number_format($this->bid->amount, 2))
                ->line("Estimated Completion: " . $this->bid->estimated_completion_time->format('M d, Y'))
                ->line("Customer: {$this->bid->jobBooking->user->name}")
                ->action('View Job Details', url("/jobs/{$this->bid->jobBooking->job_uuid}"))
                ->line('Please prepare to start work on this project. The customer will be in touch soon.')
                ->line('Thank you for using our platform!');
        }

        $locale = request()->hasHeader('Accept-Language') ? 
                  request()->header('Accept-Language') : 
                  app()->getLocale();

        $data = [
            '{{provider_name}}' => $notifiable->name,
            '{{job_title}}' => $this->bid->jobBooking->service_category,
            '{{project_code}}' => $this->bid->jobBooking->project_code,
            '{{bid_amount}}' => '$' . number_format($this->bid->amount, 2),
            '{{estimated_completion}}' => $this->bid->estimated_completion_time->format('M d, Y'),
            '{{customer_name}}' => $this->bid->jobBooking->user->name,
            '{{job_description}}' => $this->bid->jobBooking->description,
            '{{schedule_date}}' => $this->bid->jobBooking->schedule_date->format('M d, Y'),
            '{{company_name}}' => config('app.name'),
        ];

        $emailContent = str_replace(array_keys($data), array_values($data), $content->content[$locale]);
        
        return (new MailMessage) 
            ->subject($content->title[$locale])
            ->markdown('emails.email-template', [
                'content' => $content,
                'emailContent' => $emailContent,
                'locale' => $locale
            ]);
    }
    
    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'bid_id' => $this->bid->id,
            'job_booking_id' => $this->bid->jobBooking->id,
            'job_uuid' => $this->bid->jobBooking->job_uuid,
            'project_code' => $this->bid->jobBooking->project_code,
            'bid_amount' => $this->bid->amount,
            'customer_name' => $this->bid->jobBooking->user->name,
            'estimated_completion_time' => $this->bid->estimated_completion_time,
            'message' => "Congratulations! Your bid for the job '{$this->bid->jobBooking->service_category}' has been accepted.",
        ];
    }
}
