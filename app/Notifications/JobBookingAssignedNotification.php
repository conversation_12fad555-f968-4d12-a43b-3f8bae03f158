<?php

namespace App\Notifications;

use App\Models\JobBooking;
use App\Models\Bid;
use App\Models\EmailTemplate;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class JobBookingAssignedNotification extends Notification
{
    use Queueable;

    private $jobBooking;
    private $acceptedBid;

    /**
     * Create a new notification instance.
     */
    public function __construct(JobBooking $jobBooking, Bid $acceptedBid)
    {
        $this->jobBooking = $jobBooking;
        $this->acceptedBid = $acceptedBid;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $content = EmailTemplate::where('slug', 'job-booking-assigned-customer')->first();
        
        if (!$content) {
            return (new MailMessage)
                ->subject("Your Job Has Been Assigned - Project #{$this->jobBooking->project_code}")
                ->greeting('Hello ' . $notifiable->name . ',')
                ->line("Great news! Your job booking has been assigned to a provider.")
                ->line("Job Details:")
                ->line("Project Code: {$this->jobBooking->project_code}")
                ->line("Service: {$this->jobBooking->service_category}")
                ->line("Assigned Provider: {$this->acceptedBid->provider->name}")
                ->line("Agreed Amount: $" . number_format($this->acceptedBid->amount, 2))
                ->line("Estimated Completion: " . $this->acceptedBid->estimated_completion_time->format('M d, Y'))
                ->line("Scheduled Date: " . $this->jobBooking->schedule_date->format('M d, Y'))
                ->action('View Job Details', url("/job-bookings/{$this->jobBooking->job_uuid}"))
                ->line('The provider will contact you soon to coordinate the work schedule.')
                ->line('Thank you for choosing our platform!');
        }

        $locale = request()->hasHeader('Accept-Language') ? 
                  request()->header('Accept-Language') : 
                  app()->getLocale();

        $data = [
            '{{customer_name}}' => $notifiable->name,
            '{{job_title}}' => $this->jobBooking->service_category,
            '{{project_code}}' => $this->jobBooking->project_code,
            '{{provider_name}}' => $this->acceptedBid->provider->name,
            '{{agreed_amount}}' => '$' . number_format($this->acceptedBid->amount, 2),
            '{{estimated_completion}}' => $this->acceptedBid->estimated_completion_time->format('M d, Y'),
            '{{schedule_date}}' => $this->jobBooking->schedule_date->format('M d, Y'),
            '{{job_description}}' => $this->jobBooking->description,
            '{{provider_email}}' => $this->acceptedBid->provider->email,
            '{{company_name}}' => config('app.name'),
        ];

        $emailContent = str_replace(array_keys($data), array_values($data), $content->content[$locale]);
        
        return (new MailMessage) 
            ->subject($content->title[$locale])
            ->markdown('emails.email-template', [
                'content' => $content,
                'emailContent' => $emailContent,
                'locale' => $locale
            ]);
    }
    
    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'job_booking_id' => $this->jobBooking->id,
            'job_uuid' => $this->jobBooking->job_uuid,
            'project_code' => $this->jobBooking->project_code,
            'accepted_bid_id' => $this->acceptedBid->id,
            'provider_name' => $this->acceptedBid->provider->name,
            'provider_id' => $this->acceptedBid->provider_id,
            'agreed_amount' => $this->acceptedBid->amount,
            'estimated_completion_time' => $this->acceptedBid->estimated_completion_time,
            'schedule_date' => $this->jobBooking->schedule_date,
            'message' => "Your job booking '{$this->jobBooking->service_category}' has been assigned to {$this->acceptedBid->provider->name}.",
        ];
    }
}
