<?php

namespace App\Notifications;

use App\Models\JobBooking;
use App\Models\Bid;
use App\Models\EmailTemplate;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewBidPlacedNotification extends Notification
{
    use Queueable;

    private $jobBooking;
    private $bid;

    /**
     * Create a new notification instance.
     */
    public function __construct(JobBooking $jobBooking, Bid $bid)
    {
        $this->jobBooking = $jobBooking;
        $this->bid = $bid;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $content = EmailTemplate::where('slug', 'new-bid-placed-customer')->first();
        
        if (!$content) {
            return (new MailMessage)
                ->subject("New Bid Received for Job #{$this->jobBooking->project_code}")
                ->greeting('Hello ' . $notifiable->name . ',')
                ->line("You've received a new bid for your job booking '{$this->jobBooking->service_category}' from {$this->bid->provider->name}.")
                ->line("Bid Amount: $" . number_format($this->bid->amount, 2))
                ->line("Estimated Completion: " . $this->bid->estimated_completion_time->format('M d, Y'))
                ->action('View Bid Details', url("/job-bookings/{$this->jobBooking->job_uuid}/bids"))
                ->line('Thank you for using our platform!');
        }

        $locale = request()->hasHeader('Accept-Language') ? 
                  request()->header('Accept-Language') : 
                  app()->getLocale();

        $data = [
            '{{customer_name}}' => $notifiable->name,
            '{{job_title}}' => $this->jobBooking->service_category,
            '{{project_code}}' => $this->jobBooking->project_code,
            '{{provider_name}}' => $this->bid->provider->name,
            '{{bid_amount}}' => '$' . number_format($this->bid->amount, 2),
            '{{estimated_completion}}' => $this->bid->estimated_completion_time->format('M d, Y'),
            '{{job_description}}' => $this->jobBooking->description,
            '{{company_name}}' => config('app.name'),
        ];

        $emailContent = str_replace(array_keys($data), array_values($data), $content->content[$locale]);
        
        return (new MailMessage) 
            ->subject($content->title[$locale])
            ->markdown('emails.email-template', [
                'content' => $content,
                'emailContent' => $emailContent,
                'locale' => $locale
            ]);
    }
    
    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'job_booking_id' => $this->jobBooking->id,
            'job_uuid' => $this->jobBooking->job_uuid,
            'project_code' => $this->jobBooking->project_code,
            'bid_id' => $this->bid->id,
            'provider_name' => $this->bid->provider->name,
            'bid_amount' => $this->bid->amount,
            'estimated_completion_time' => $this->bid->estimated_completion_time,
            'message' => "You've received a new bid for your job booking '{$this->jobBooking->service_category}' from {$this->bid->provider->name}.",
        ];
    }
}
