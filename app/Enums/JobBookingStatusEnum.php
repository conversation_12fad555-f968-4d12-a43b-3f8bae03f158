<?php

namespace App\Enums;

enum JobBookingStatusEnum: string
{
    const PENDING = 'pending';
    const OPEN = 'open';
    const ASSIGNED = 'assigned';
    const IN_PROGRESS = 'in_progress';
    const COMPLETED = 'completed';
    const CANCELLED = 'cancelled';
    const EXPIRED = 'expired';

    /**
     * Get all available status values
     */
    public static function getAllStatuses(): array
    {
        return [
            self::PENDING,
            self::OPEN,
            self::ASSIGNED,
            self::IN_PROGRESS,
            self::COMPLETED,
            self::CANCELLED,
            self::EXPIRED,
        ];
    }

    /**
     * Get active statuses (not completed, cancelled, or expired)
     */
    public static function getActiveStatuses(): array
    {
        return [
            self::PENDING,
            self::OPEN,
            self::ASSIGNED,
            self::IN_PROGRESS,
        ];
    }

    /**
     * Get final statuses (completed, cancelled, or expired)
     */
    public static function getFinalStatuses(): array
    {
        return [
            self::COMPLETED,
            self::CANCELLED,
            self::EXPIRED,
        ];
    }

    /**
     * Check if status allows bidding
     */
    public static function allowsBidding(string $status): bool
    {
        return in_array($status, [self::PENDING, self::OPEN]);
    }

    /**
     * Check if status is final (no further changes allowed)
     */
    public static function isFinal(string $status): bool
    {
        return in_array($status, self::getFinalStatuses());
    }

    /**
     * Get the next possible statuses from current status
     */
    public static function getNextStatuses(string $currentStatus): array
    {
        return match ($currentStatus) {
            self::PENDING => [self::OPEN, self::CANCELLED],
            self::OPEN => [self::ASSIGNED, self::CANCELLED, self::EXPIRED],
            self::ASSIGNED => [self::IN_PROGRESS, self::CANCELLED],
            self::IN_PROGRESS => [self::COMPLETED, self::CANCELLED],
            default => [], // Final statuses have no next statuses
        };
    }
}
