<?php

namespace App\Enums;

enum BidStatusEnum:string {
  const REQUESTED = 'requested';
  const ACCEPTED = 'accepted';
  const REJECTED = 'rejected';
  const WITHDRAWN = 'withdrawn';

  /**
   * Get all available status values
   */
  public static function getAllStatuses(): array
  {
      return [
          self::REQUESTED,
          self::ACCEPTED,
          self::REJECTED,
          self::WITHDRAWN,
      ];
  }

  /**
   * Get active statuses (not rejected or withdrawn)
   */
  public static function getActiveStatuses(): array
  {
      return [
          self::REQUESTED,
          self::ACCEPTED,
      ];
  }

  /**
   * Get final statuses (rejected or withdrawn)
   */
  public static function getFinalStatuses(): array
  {
      return [
          self::REJECTED,
          self::WITHDRAWN,
      ];
  }

  /**
   * Check if status is final (no further changes allowed)
   */
  public static function isFinal(string $status): bool
  {
      return in_array($status, self::getFinalStatuses());
  }

  /**
   * Check if status is active (can still be modified)
   */
  public static function isActive(string $status): bool
  {
      return in_array($status, self::getActiveStatuses());
  }
}
