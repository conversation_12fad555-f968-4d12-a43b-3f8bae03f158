<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class JobBidResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'job_booking_id' => $this->job_booking_id,
            'provider_id' => $this->provider_id,
            'amount' => $this->amount,
            'description' => $this->description,
            'status' => $this->status,
            'status_display' => $this->getStatusDisplayName(),
            'estimated_completion_time' => $this->estimated_completion_time,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'is_winning_bid' => $this->isWinningBid(),
            'is_lowest_bid' => $this->isLowestBid(),
            'ranking' => $this->getRanking(),
            'available_actions' => $this->getAvailableActions(),
            'job_booking' => $this->whenLoaded('jobBooking', function () {
                return [
                    'id' => $this->jobBooking->id,
                    'job_uuid' => $this->jobBooking->job_uuid,
                    'project_code' => $this->jobBooking->project_code,
                    'job_type' => $this->jobBooking->job_type,
                    'service_category' => $this->jobBooking->service_category,
                    'description' => $this->jobBooking->description,
                    'status' => $this->jobBooking->status,
                    'schedule_date' => $this->jobBooking->schedule_date,
                    'address' => $this->jobBooking->address,
                    'city' => $this->jobBooking->city,
                    'state' => $this->jobBooking->state,
                ];
            }),
            'provider' => $this->whenLoaded('provider', function () {
                return [
                    'id' => $this->provider->id,
                    'first_name' => $this->provider->first_name,
                    'last_name' => $this->provider->last_name,
                    'email' => $this->provider->email,
                    'phone' => $this->provider->phone,
                ];
            }),
        ];
    }
}
