<?php

namespace App\Http\Controllers\API;

use App\Enums\RoleEnum;
use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Requests\API\StoreJobBidRequest;
use App\Http\Requests\API\UpdateJobBidRequest;
use App\Http\Resources\JobBidResource;
use App\Models\Bid;
use App\Models\JobBooking;
use App\Notifications\NewBidPlacedNotification;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

class JobBidController extends Controller
{
    /**
     * Display a listing of bids for a specific job booking.
     */
    public function index(JobBooking $jobBooking): JsonResponse
    {
        try {
            $bids = $jobBooking->bids()
                ->with(['provider', 'jobBooking'])
                ->orderBy('amount', 'asc')
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json([
                'success' => true,
                'data' => JobBidResource::collection($bids),
                'meta' => [
                    'total_bids' => $bids->count(),
                    'lowest_bid' => $bids->min('amount'),
                    'highest_bid' => $bids->max('amount'),
                    'average_bid' => $bids->avg('amount'),
                ]
            ]);
        } catch (Exception $e) {
            Log::error('Failed to fetch job bids: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FETCH_BIDS_FAILED',
                    'message' => 'Failed to fetch bids for this job booking.',
                ]
            ], 500);
        }
    }

    /**
     * Store a newly created bid for a job booking.
     */
    public function store(StoreJobBidRequest $request, JobBooking $jobBooking): JsonResponse
    {
        try {
            $bid = Bid::create([
                'job_booking_id' => $jobBooking->id,
                'provider_id' => Helpers::getCurrentUserId(),
                'amount' => $request->amount,
                'description' => $request->description,
                'estimated_completion_time' => $request->estimated_completion_time,
                'status' => 'requested',
            ]);

            $bid->load(['provider', 'jobBooking']);

            // Dispatch notification to the job booking owner (customer)
            try {
                $jobBooking->user->notify(new NewBidPlacedNotification($jobBooking, $bid));
            } catch (Exception $notificationException) {
                // Log notification failure but don't fail the main operation
                Log::warning('Failed to send new bid notification: ' . $notificationException->getMessage());
            }

            return response()->json([
                'success' => true,
                'data' => new JobBidResource($bid),
                'message' => 'Bid placed successfully.'
            ], 201);
        } catch (Exception $e) {
            Log::error('Failed to create job bid: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'CREATE_BID_FAILED',
                    'message' => 'Failed to place bid.',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Display the specified bid.
     */
    public function show(Bid $bid): JsonResponse
    {
        try {
            $bid->load(['provider', 'jobBooking']);

            return response()->json([
                'success' => true,
                'data' => new JobBidResource($bid)
            ]);
        } catch (Exception $e) {
            Log::error('Failed to fetch job bid: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'FETCH_BID_FAILED',
                    'message' => 'Failed to fetch bid details.',
                ]
            ], 500);
        }
    }

    /**
     * Update the specified bid.
     */
    public function update(UpdateJobBidRequest $request, Bid $bid): JsonResponse
    {
        try {
            $bid->update($request->validated());
            $bid->load(['provider', 'jobBooking']);

            return response()->json([
                'success' => true,
                'data' => new JobBidResource($bid),
                'message' => 'Bid updated successfully.'
            ]);
        } catch (Exception $e) {
            Log::error('Failed to update job bid: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'UPDATE_BID_FAILED',
                    'message' => 'Failed to update bid.',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Remove the specified bid (withdraw).
     */
    public function destroy(Bid $bid): JsonResponse
    {
        try {
            // Check authorization
            if (Helpers::getCurrentRoleName() !== RoleEnum::PROVIDER) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'UNAUTHORIZED',
                        'message' => 'Only providers can withdraw bids.',
                    ]
                ], 403);
            }

            if ($bid->provider_id !== Helpers::getCurrentUserId()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'FORBIDDEN',
                        'message' => 'You can only withdraw your own bids.',
                    ]
                ], 403);
            }

            if (!$bid->canWithdraw()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'INVALID_STATUS',
                        'message' => 'This bid cannot be withdrawn. Only pending bids can be withdrawn.',
                    ]
                ], 422);
            }

            $bid->withdraw();

            return response()->json([
                'success' => true,
                'message' => 'Bid withdrawn successfully.'
            ]);
        } catch (Exception $e) {
            Log::error('Failed to withdraw job bid: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'WITHDRAW_BID_FAILED',
                    'message' => 'Failed to withdraw bid.',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }
}
