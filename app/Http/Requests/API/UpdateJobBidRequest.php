<?php

namespace App\Http\Requests\API;

use App\Enums\RoleEnum;
use App\Helpers\Helpers;
use Illuminate\Foundation\Http\FormRequest;

class UpdateJobBidRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Check if user is a provider
        if (Helpers::getCurrentRoleName() !== RoleEnum::PROVIDER) {
            return false;
        }

        // Get the bid from route parameter
        $bid = $this->route('bid');
        
        // Check if user owns the bid
        if (!$bid || $bid->provider_id !== Helpers::getCurrentUserId()) {
            return false;
        }

        // Check if bid can be updated (only 'requested' bids can be updated)
        if (!$bid->canWithdraw()) {
            return false;
        }

        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'amount' => ['sometimes', 'numeric', 'min:0.01'],
            'description' => ['sometimes', 'string', 'max:1000'],
            'estimated_completion_time' => ['sometimes', 'date', 'after:now'],
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'amount.numeric' => 'Bid amount must be a valid number.',
            'amount.min' => 'Bid amount must be greater than 0.',
            'description.string' => 'Bid description must be a string.',
            'description.max' => 'Bid description cannot exceed 1000 characters.',
            'estimated_completion_time.date' => 'Estimated completion time must be a valid date.',
            'estimated_completion_time.after' => 'Estimated completion time must be in the future.',
        ];
    }

    /**
     * Get custom validation error messages for authorization failures.
     */
    public function failedAuthorization()
    {
        $bid = $this->route('bid');
        
        if (Helpers::getCurrentRoleName() !== RoleEnum::PROVIDER) {
            abort(403, 'Only providers can update bids.');
        }

        if (!$bid || $bid->provider_id !== Helpers::getCurrentUserId()) {
            abort(403, 'You can only update your own bids.');
        }

        if (!$bid->canWithdraw()) {
            abort(422, 'This bid cannot be updated. Only pending bids can be modified.');
        }

        abort(403, 'You are not authorized to update this bid.');
    }
}
