<?php

namespace App\Http\Requests\API;

use App\Enums\RoleEnum;
use App\Helpers\Helpers;
use App\Models\Bid;
use Illuminate\Foundation\Http\FormRequest;

class StoreJobBidRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Check if user is a provider
        if (Helpers::getCurrentRoleName() !== RoleEnum::PROVIDER) {
            return false;
        }

        // Get the job booking from route parameter
        $jobBooking = $this->route('job_booking');
        
        // Check if job booking allows bidding
        if (!$jobBooking || !$jobBooking->allowsBidding()) {
            return false;
        }

        // Check if provider already has a bid for this job booking
        $existingBid = Bid::where('job_booking_id', $jobBooking->id)
            ->where('provider_id', Helpers::getCurrentUserId())
            ->first();

        if ($existingBid) {
            return false;
        }

        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'amount' => ['required', 'numeric', 'min:0.01'],
            'description' => ['required', 'string', 'max:1000'],
            'estimated_completion_time' => ['required', 'date', 'after:now'],
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'amount.required' => 'Bid amount is required.',
            'amount.numeric' => 'Bid amount must be a valid number.',
            'amount.min' => 'Bid amount must be greater than 0.',
            'description.required' => 'Bid description is required.',
            'description.max' => 'Bid description cannot exceed 1000 characters.',
            'estimated_completion_time.required' => 'Estimated completion time is required.',
            'estimated_completion_time.date' => 'Estimated completion time must be a valid date.',
            'estimated_completion_time.after' => 'Estimated completion time must be in the future.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'provider_id' => Helpers::getCurrentUserId(),
            'job_booking_id' => $this->route('job_booking')->id,
            'status' => 'requested',
        ]);
    }

    /**
     * Get custom validation error messages for authorization failures.
     */
    public function failedAuthorization()
    {
        $jobBooking = $this->route('job_booking');
        
        if (Helpers::getCurrentRoleName() !== RoleEnum::PROVIDER) {
            abort(403, 'Only providers can place bids.');
        }

        if (!$jobBooking || !$jobBooking->allowsBidding()) {
            abort(422, 'This job booking is not accepting bids.');
        }

        $existingBid = Bid::where('job_booking_id', $jobBooking->id)
            ->where('provider_id', Helpers::getCurrentUserId())
            ->first();

        if ($existingBid) {
            abort(422, 'You have already placed a bid for this job booking.');
        }

        abort(403, 'You are not authorized to place a bid on this job booking.');
    }
}
