<?php

namespace Database\Factories;

use App\Models\JobBooking;
use App\Models\User;
use App\Enums\JobBookingStatusEnum;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\JobBooking>
 */
class JobBookingFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = JobBooking::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'job_type' => $this->faker->randomElement(['send_bids', 'find_providers']),
            'property_type' => $this->faker->randomElement(['residential', 'commercial', 'industrial']),
            'service_category' => $this->faker->randomElement(['cleaning', 'plumbing', 'electrical', 'painting']),
            'service_tasks' => ['task1', 'task2'],
            'description' => $this->faker->paragraph(),
            'schedule_date' => $this->faker->dateTimeBetween('now', '+30 days'),
            'time_preference' => $this->faker->randomElement(['morning', 'afternoon', 'evening']),
            'frequency' => $this->faker->randomElement(['one-time', 'recurring']),
            'recurring_frequency' => null,
            'address' => $this->faker->streetAddress(),
            'city' => $this->faker->city(),
            'state' => $this->faker->state(),
            'zip_code' => $this->faker->postcode(),
            'contact_name' => $this->faker->name(),
            'contact_email' => $this->faker->email(),
            'contact_phone' => $this->faker->phoneNumber(),
            'status' => JobBookingStatusEnum::PENDING,
            'user_id' => User::factory(),
        ];
    }

    /**
     * Indicate that the job booking is open for bidding.
     */
    public function open(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => JobBookingStatusEnum::OPEN,
        ]);
    }

    /**
     * Indicate that the job booking is assigned.
     */
    public function assigned(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => JobBookingStatusEnum::ASSIGNED,
        ]);
    }

    /**
     * Indicate that the job booking is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => JobBookingStatusEnum::COMPLETED,
        ]);
    }

    /**
     * Indicate that the job booking is cancelled.
     */
    public function cancelled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => JobBookingStatusEnum::CANCELLED,
        ]);
    }
}
