<?php

namespace Database\Factories;

use App\Models\Bid;
use App\Models\JobBooking;
use App\Models\User;
use App\Enums\BidStatusEnum;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Bid>
 */
class BidFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Bid::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'job_booking_id' => JobBooking::factory(),
            'provider_id' => User::factory(),
            'amount' => $this->faker->randomFloat(2, 50, 1000),
            'description' => $this->faker->paragraph(),
            'status' => BidStatusEnum::REQUESTED,
            'estimated_completion_time' => $this->faker->dateTimeBetween('+1 day', '+30 days'),
        ];
    }

    /**
     * Indicate that the bid is requested.
     */
    public function requested(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => BidStatusEnum::REQUESTED,
        ]);
    }

    /**
     * Indicate that the bid is accepted.
     */
    public function accepted(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => BidStatusEnum::ACCEPTED,
        ]);
    }

    /**
     * Indicate that the bid is rejected.
     */
    public function rejected(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => BidStatusEnum::REJECTED,
        ]);
    }

    /**
     * Indicate that the bid is withdrawn.
     */
    public function withdrawn(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => BidStatusEnum::WITHDRAWN,
        ]);
    }
}
