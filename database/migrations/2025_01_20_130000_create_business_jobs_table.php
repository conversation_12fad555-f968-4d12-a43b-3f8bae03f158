<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('business_jobs', function (Blueprint $table) {
            $table->id();
            $table->uuid('job_uuid')->unique();
            $table->unsignedBigInteger('job_booking_id');
            $table->unsignedBigInteger('bid_id');
            $table->unsignedBigInteger('booking_id')->nullable(); // Links to the actual booking record
            $table->unsignedBigInteger('customer_id'); // User who created the job booking
            $table->unsignedBigInteger('provider_id'); // Provider who won the bid
            $table->enum('status', ['assigned', 'in_progress', 'completed', 'cancelled'])->default('assigned');
            $table->decimal('agreed_amount', 10, 2); // Amount from the accepted bid
            $table->timestamp('estimated_completion_time')->nullable();
            $table->timestamp('actual_start_time')->nullable();
            $table->timestamp('actual_completion_time')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('job_booking_id')->references('id')->on('job_bookings')->onDelete('cascade');
            $table->foreign('bid_id')->references('id')->on('bids')->onDelete('cascade');
            $table->foreign('booking_id')->references('id')->on('bookings')->onDelete('set null');
            $table->foreign('customer_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('provider_id')->references('id')->on('users')->onDelete('cascade');

            // Indexes for performance
            $table->index('job_booking_id');
            $table->index('bid_id');
            $table->index('booking_id');
            $table->index('customer_id');
            $table->index('provider_id');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('business_jobs');
    }
};
