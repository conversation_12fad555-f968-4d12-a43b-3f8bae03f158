<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update existing 'pending' status records to maintain consistency
        DB::table('job_bookings')->where('status', 'pending')->update(['status' => 'pending']);
        
        // Modify the status column to use enum with proper workflow states
        DB::statement("ALTER TABLE job_bookings MODIFY COLUMN status ENUM('pending', 'open', 'assigned', 'in_progress', 'completed', 'cancelled', 'expired') NOT NULL DEFAULT 'pending'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Update any new status values back to 'pending' before reverting
        DB::table('job_bookings')
            ->whereIn('status', ['open', 'assigned', 'in_progress', 'completed', 'cancelled', 'expired'])
            ->update(['status' => 'pending']);
        
        // Revert the status column to original string type
        DB::statement("ALTER TABLE job_bookings MODIFY COLUMN status VARCHAR(255) NOT NULL DEFAULT 'pending'");
    }
};
